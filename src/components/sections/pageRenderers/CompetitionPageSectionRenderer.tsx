import React from "react";
import {
	SectionRendererProps,
	SectionType,
} from "../../../types/sectionTypes";
import {
	AdSection<PERSON>enderer,
	StaticCarouselSectionRenderer,
	LiveSectionRenderer,
	ClubsSectionRenderer,
	DynamicCarouselSectionRenderer,
	DynamicGridWithCategorySectionRenderer,
	FeaturedVideoSectionRenderer,
} from "../renderers";

/**
 * Common Section Types across all Competition Pages:
 *
 * 1. section_static_carousel - Used for:
 *    - Hero/slider sections (top carousels)
 *    - Categories rows
 *    - Clubs grids (with itemsDisplayType: "portrait")
 *
 * 2. section_dynamic_live - Used for:
 *    - Live matches sections
 *
 * 3. section_dynamic_carousel - Used for:
 *    - Debrief & reactions videos
 *    - Pre-match content
 *    - Other dynamic video content
 *
 * 4. section_static_ad - Used for:
 *    - Partners/TV channel ad banners
 *
 * 5. section_dynamic_grid_with_category - Used for:
 *    - Twitch content
 *    - Other grid-based content with category filtering
 */

/**
 * CompetitionPageSectionRenderer
 *
 * A universal section renderer for all Competition Pages that handles these section types:
 * - section_static_carousel: For displaying fixed content like featured videos, categories, and clubs
 * - section_dynamic_live: For displaying live matches
 * - section_dynamic_carousel: For displaying dynamic content
 * - section_static_ad: For displaying advertisements
 * - section_dynamic_grid_with_category: For displaying content with category filtering
 *
 * This renderer works for all competition pages since they all use the same section types,
 * just with different content and configurations.
 */
const CompetitionPageSectionRenderer: React.FC<
	SectionRendererProps
> = ({
	section,
	onItemPress,
	upcomingEvents = [],
	isAuthenticated = false,
}) => {
	// Log section information
	// console.log(
	// 	`Rendering section: ${section._kenticoItemType} (${section._kenticoCodename})`
	// );

	// Handle null or undefined section
	if (!section) {
		console.warn("Received null or undefined section");
		return null;
	}

	try {
		// Special case: Club sections (static carousel with portrait display type)
		if (
			section._kenticoItemType === SectionType.STATIC_CAROUSEL &&
			(section as any).itemsDisplayType === "portrait"
		) {
			console.log(
				`Rendering club section: ${section._kenticoCodename}`
			);
			return (
				<ClubsSectionRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		}

		// Special case: Featured video section at the top of competition pages
		// These sections typically have codenames like "page_liqui_moly_starligue___slider_haut_de_page"
		if (
			section._kenticoItemType === SectionType.STATIC_CAROUSEL &&
			section._kenticoCodename &&
			(section._kenticoCodename.includes("slider_haut_de_page") ||
				section._kenticoCodename.includes("___slider") ||
				section._kenticoCodename.includes("___hero"))
		) {
			return (
				<FeaturedVideoSectionRenderer
					data={section}
					onPress={onItemPress}
					isAuthenticated={isAuthenticated}
				/>
			);
		}

		// Main switch case to handle all section types across all competition pages
		switch (section._kenticoItemType) {
			case SectionType.STATIC_CAROUSEL:
				// For static carousels (featured videos, categories, etc.)
				return (
					<StaticCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
						isAuthenticated={isAuthenticated}
					/>
				);

			case SectionType.DYNAMIC_LIVE:
				// For live matches - pass both section data and upcoming events
				return (
					<LiveSectionRenderer
						data={section}
						onPress={onItemPress}
						upcomingEvents={upcomingEvents}
						isAuthenticated={isAuthenticated}
					/>
				);

			case SectionType.DYNAMIC_CAROUSEL:
				// For dynamic content carousels
				return (
					<DynamicCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
						isAuthenticated={isAuthenticated}
					/>
				);

			case SectionType.STATIC_AD:
				// For advertisements
				return (
					<AdSectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);

			case SectionType.DYNAMIC_GRID_WITH_CATEGORY:
				// For grid-based content with category filtering
				return (
					<DynamicGridWithCategorySectionRenderer
						data={section}
						onPress={onItemPress}
						isAuthenticated={isAuthenticated}
					/>
				);

			case "web_section___hero":
				// For hero sections (web-specific)
				return (
					<StaticCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
						isAuthenticated={isAuthenticated}
					/>
				);

			default:
				// For unknown section types, use StaticCarouselSectionRenderer as a fallback
				console.warn(
					`Using default renderer for unknown section type: ${section._kenticoItemType} (${section._kenticoCodename})`
				);
				return (
					<StaticCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
						isAuthenticated={isAuthenticated}
					/>
				);
		}
	} catch (error) {
		// Error handling to prevent crashes
		console.error(
			`Error rendering section ${section._kenticoCodename}:`,
			error
		);
		// Return null in case of error to prevent app crash
		return null;
	}
};

export default React.memo(CompetitionPageSectionRenderer);
