import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";

/**
 * PaidContentOverlay Component
 *
 * A reusable overlay component that displays lock icons for paid content.
 * Shows overlay based on user authentication status:
 * - For non-authenticated users: full red overlay with centered lock icon for paid content
 * - For authenticated users: no overlay (they have access to all paid content)
 *
 * @param isAuthenticated - Whether the user is currently authenticated
 * @param isPaid - Whether the content requires payment
 */
interface PaidContentOverlayProps {
	isAuthenticated: boolean;
	isPaid: boolean;
}

const PaidContentOverlay: React.FC<PaidContentOverlayProps> = ({
	isAuthenticated,
	isPaid,
}) => {
	// Don't render anything if content is not paid
	if (!isPaid) {
		return null;
	}

	// Don't render anything if user is authenticated (they have access to all paid content)
	if (isAuthenticated) {
		return null;
	}

	// Only non-authenticated users see red overlay with centered lock for paid content
	return (
		<View style={styles.lockOverlay}>
			<View style={styles.lockIconContainerLarge}>
				<Text style={styles.lockIconLarge}>🔒</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	/**
	 * Full overlay for non-authenticated users
	 * Covers the entire thumbnail with a red tint
	 */
	lockOverlay: {
		position: "absolute", // Positions over thumbnail
		top: 0, // Aligns to top
		left: 0, // Aligns to left
		right: 0, // Stretches to right
		bottom: 0, // Stretches to bottom
		borderRadius: scale(8), // Rounds corners to match container
		justifyContent: "center", // Centers lock icon vertically
		alignItems: "center", // Centers lock icon horizontally
		backgroundColor: "rgba(255, 0, 0, 0.2)", // Subtle red tint for premium content
	},

	/**
	 * Large centered lock icon container for non-authenticated users
	 */
	lockIconContainerLarge: {
		backgroundColor: "rgba(0, 0, 0, 0.7)", // Dark background for visibility
		borderRadius: scale(25), // Circular background
		padding: scale(12), // Padding around icon
		justifyContent: "center",
		alignItems: "center",
	},

	/**
	 * Large lock icon text for non-authenticated users
	 */
	lockIconLarge: {
		fontSize: scale(24), // Large icon size
		color: "#FFFFFF", // White color for contrast
	},
});

export default PaidContentOverlay;
