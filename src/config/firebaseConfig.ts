/**
 * Firebase Configuration
 * Configuration extracted from google-services.json for Firebase authentication
 */

import { initializeApp, FirebaseApp } from "firebase/app";
import { getAuth, Auth } from "firebase/auth";

// Firebase configuration object extracted from your google-services.json
const firebaseConfig = {
	apiKey: "AIzaSyA3jiPaaKD3tHiraXttw2zMnykydh7x-kw",
	authDomain: "white-label-da752.firebaseapp.com", // Constructed from project_id
	projectId: "white-label-da752",
	storageBucket: "white-label-da752.appspot.com",
	messagingSenderId: "417504702949", // From project_number
	appId: "1:417504702949:android:1b8f667a6d2039f268ff0c", // From mobilesdk_app_id
};

// Initialize Firebase app
let firebaseApp: FirebaseApp;
let firebaseAuth: Auth;

/**
 * Initialize Firebase application
 * This should be called once when the app starts
 */
export const initializeFirebase = (): { app: FirebaseApp; auth: Auth } => {
	try {
		// Initialize Firebase app if not already initialized
		if (!firebaseApp) {
			firebaseApp = initializeApp(firebaseConfig);
			console.log("Firebase app initialized successfully");
		}

		// Initialize Firebase Auth if not already initialized
		if (!firebaseAuth) {
			firebaseAuth = getAuth(firebaseApp);
			console.log("Firebase Auth initialized successfully");
		}

		return {
			app: firebaseApp,
			auth: firebaseAuth,
		};
	} catch (error) {
		console.error("Error initializing Firebase:", error);
		throw error;
	}
};

/**
 * Get Firebase Auth instance
 * Initializes Firebase if not already done
 */
export const getFirebaseAuth = (): Auth => {
	if (!firebaseAuth) {
		const { auth } = initializeFirebase();
		return auth;
	}
	return firebaseAuth;
};

/**
 * Get Firebase App instance
 * Initializes Firebase if not already done
 */
export const getFirebaseApp = (): FirebaseApp => {
	if (!firebaseApp) {
		const { app } = initializeFirebase();
		return app;
	}
	return firebaseApp;
};

export default firebaseConfig;
