/**
 * Firebase Authentication Service
 * Handles real Firebase authentication and provides the Firebase JWT token
 * that gets exchanged for app access tokens via the OnRewind API
 */

import { signInWithEmailAndPassword, User } from "firebase/auth";
import { getFirebaseAuth } from "../config/firebaseConfig";

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Real Firebase JWT token for OnRewind API
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

/**
 * Firebase Authentication Service
 * Performs real Firebase authentication and returns a Firebase JWT token
 * that will be exchanged for app tokens via OnRewind API
 */
export class FirebaseAuth {
	/**
	 * Sign in with email and password using real Firebase authentication
	 * Returns a fresh Firebase JWT token for OnRewind API authentication
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log("Firebase: Authenticating user with email:", email);

			// Basic validation
			if (!email || !password) {
				throw {
					code: "auth/invalid-credentials",
					message: "Email and password are required",
				};
			}

			// Basic email validation
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(email)) {
				throw {
					code: "auth/invalid-email",
					message: "Please enter a valid email address",
				};
			}

			// Basic password validation
			if (password.length < 6) {
				throw {
					code: "auth/weak-password",
					message: "Password must be at least 6 characters long",
				};
			}

			// Get Firebase Auth instance
			const auth = getFirebaseAuth();

			// Perform Firebase authentication
			console.log("Firebase: Signing in with Firebase Auth...");
			const userCredential = await signInWithEmailAndPassword(
				auth,
				email,
				password
			);

			const firebaseUser: User = userCredential.user;
			console.log(
				"Firebase: Authentication successful for UID:",
				firebaseUser.uid
			);

			// Get fresh Firebase JWT token
			console.log("Firebase: Getting fresh ID token...");
			const idToken = await firebaseUser.getIdToken(true); // Force refresh to get fresh token
			console.log(
				"Firebase: Fresh token obtained (first 50 chars):",
				idToken.substring(0, 50) + "..."
			);

			// Create user object from Firebase user
			const user: FirebaseUser = {
				uid: firebaseUser.uid,
				email: firebaseUser.email || email,
				displayName: firebaseUser.displayName || undefined,
				emailVerified: firebaseUser.emailVerified,
			};

			console.log("Firebase: User object created:", {
				uid: user.uid,
				email: user.email,
				displayName: user.displayName,
				emailVerified: user.emailVerified,
			});

			return {
				user,
				token: idToken,
			};
		} catch (error: any) {
			console.error("Firebase authentication error:", error);

			// Handle Firebase-specific errors
			if (error.code && error.code.startsWith("auth/")) {
				const firebaseError: FirebaseAuthError = {
					code: error.code,
					message: this.getReadableErrorMessage(error.code),
				};
				throw firebaseError;
			}

			// If error already has our format, throw it
			if (error.code && error.message) {
				throw error as FirebaseAuthError;
			}

			// Convert other errors to our format
			const firebaseError: FirebaseAuthError = {
				code: error.code || "auth/unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

	/**
	 * Convert Firebase error codes to user-friendly messages
	 */
	private static getReadableErrorMessage(errorCode: string): string {
		switch (errorCode) {
			// Firebase Auth v9+ error codes
			case "auth/user-not-found":
				return "No account found with this email address.";
			case "auth/wrong-password":
				return "Incorrect password. Please try again.";
			case "auth/invalid-password":
				return "Incorrect password. Please try again.";
			case "auth/user-disabled":
				return "This account has been disabled.";
			case "auth/too-many-requests":
				return "Too many failed attempts. Please try again later.";
			case "auth/invalid-email":
				return "Please enter a valid email address.";
			case "auth/weak-password":
				return "Password must be at least 6 characters long.";
			case "auth/email-already-in-use":
				return "An account with this email already exists.";
			case "auth/operation-not-allowed":
				return "Email/password authentication is not enabled.";
			case "auth/invalid-credential":
				return "Invalid email or password. Please try again.";
			case "auth/network-request-failed":
				return "Network error. Please check your connection and try again.";
			case "auth/timeout":
				return "Request timed out. Please try again.";
			// Legacy error codes (for backward compatibility)
			case "EMAIL_NOT_FOUND":
				return "No account found with this email address.";
			case "INVALID_PASSWORD":
				return "Incorrect password. Please try again.";
			case "USER_DISABLED":
				return "This account has been disabled.";
			case "TOO_MANY_ATTEMPTS_TRY_LATER":
				return "Too many failed attempts. Please try again later.";
			case "INVALID_EMAIL":
				return "Please enter a valid email address.";
			case "WEAK_PASSWORD":
				return "Password must be at least 6 characters long.";
			case "EMAIL_EXISTS":
				return "An account with this email already exists.";
			default:
				return "Authentication failed. Please try again.";
		}
	}
}
